<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;
use App\Models\TenderResult;
use Livewire\WithFileUploads;
use App\Traits\InteractsWithBanner;
use Illuminate\Validation\Rules\File;

class ComparativeStatement extends Component
{
    use WithFileUploads;
    use InteractsWithBanner;

    public $tenderId;
    public $comparativeStatement;

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function save()
    {
        $this->validate([
            'comparativeStatement' => [
                'required',
                File::types(['pdf'])->max(8 * 1024),
            ],
        ]);

        try {
            $filePath = $this->comparativeStatement->store('/', 'comparative-statements');

            $this->tender->update([
                'comparative_statement' => $filePath,
            ]);

            $this->banner('Comparative Statement added.');

            return redirect()->route('tenders.biddings', $this->tenderId);
        } catch (\Exception $e) {
            logger()->error('Comparative Statement Upload: Error uploading file', [
                'tender_id' => $this->tenderId,
                'tender_uin' => $this->tender->tender_uin ?? 'unknown',
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);

            $this->addError('comparativeStatement', 'Failed to upload comparative statement. Please try again.');
            return; // Prevent redirect on error
        }
    }

    public function render()
    {
        return view('livewire.tenders.comparative-statement');
    }
}
