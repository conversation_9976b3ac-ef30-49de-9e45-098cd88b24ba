<?php

namespace App\Http\Livewire\Tenders;

// use App\Http\Livewire\Modal;

use App\Models\Tender;
use App\Models\Bidding;
use Livewire\Component;
use App\Models\OtpOfficial;
use App\Services\SmsService;
use App\Enums\TenderStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Notifications\TenderBidOpened;

class OpenTender extends Component
{
    public $tenderId;
    public $otp;

    public $showOpenTenderModal = false;

    protected $listeners = [
        'showOpenTenderModal'
    ];

    public function showOpenTenderModal()
    {
        // Validate that an OTP official exists for this department
        if (is_null($this->officialUserPhone)) {
            $this->notify('No OTP official found for this department. Please configure an official person first.', 'error');
            return;
        }

        if (is_null($this->tender->tender_opening_otp)) {
            $otpGenerated = mt_rand(111111, 999999);
        } else {
            $otpGenerated = $this->tender->tender_opening_otp;
        }

        try {
            DB::transaction(function() use ($otpGenerated) {
                $message = "Your OTP for tender opening is: {$otpGenerated}. Tender Number: {$this->tender->tender_uin}. E-Tender Portal";

                SmsService::make($message)
                    ->to($this->officialUserPhone)
                    ->send();

                $this->tender->update([
                    'tender_opening_otp' => $otpGenerated
                ]);
            });
        } catch (\Exception $e) {
            logger()->error('Financial Bid Opening: Error in sending OTP', [
                'tender_id' => $this->tender->id,
                'tender_uin' => $this->tender->tender_uin,
                'official_phone' => $this->officialUserPhone,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            $this->notify('Failed to send OTP. Please try again.', 'error');
            return;
        }

        $this->resetErrorBag();
        $this->showOpenTenderModal = true;
    }

    public function closeModal()
    {
        $this->resetErrorBag();
        $this->showOpenTenderModal = false;
    }

    public function verifyOtp()
    {
        // TODO: must match with the generated OTP send
        $otp = $this->tender->tender_opening_otp;
        
        $this->validate([
            'otp' => ['required', 'digits:6', function ($attribute, $value, $fail) use ($otp) {
                if ($value != $otp) {
                    return $fail('The '. $attribute . ' is invalid.');
                }
            }]
        ]);

        try {
            // Find all the companies associated with the bidding that are accepted for this specific tender
            $biddings = Bidding::with(['user', 'tender'])
                ->where('tender_id', $this->tenderId)
                ->accepted()
                ->get();

            $biddings->each(function ($bidding) {
                $bidding->user->notify(
                    new TenderBidOpened(
                        $this->tender->tender_uin,
                        now()->toFormattedDateString()
                    )
                );
            });

            $this->tender->update([
                'tender_opening_otp' => null,
                'tender_opened_at' => now()
                // Note: Keeping tender_status unchanged for backward compatibility
            ]);

            $this->closeModal();

            $this->emit('refreshBiddings');

            $this->notify('Tender successfully opened.');
        } catch (\Exception $e) {
            logger()->error('Financial Bid Opening: Error in opening tender', [
                'tender_id' => $this->tender->id,
                'tender_uin' => $this->tender->tender_uin,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            $this->notify('Something went wrong. Please try again.', 'error');
        }
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function getOfficialUserPhoneProperty()
    {
        $otpOfficial = OtpOfficial::where('department_id', $this->user->department_id)->first();

        return $otpOfficial ? $otpOfficial->phone : null;
    }

    public function render()
    {
        return view('livewire.tenders.open-tender');
    }
}
