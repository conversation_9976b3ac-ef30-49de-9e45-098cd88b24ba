<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;
use Illuminate\Support\Str;
use Spatie\SimpleExcel\SimpleExcelWriter;

class Bidding extends Component
{
    public $tenderId;
    public $tenderNumber;
    
    public $name;
    public $biddings;

    protected $listeners = [
        'refreshBiddings' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        $this->tenderId = $tender->id;
        $this->tenderNumber = $tender->tender_number;

        // Load appropriate biddings based on tender status
        // Check if technical bidding is opened (either field can indicate this)
        $technicalBiddingOpened = $tender->tender_document_opened_at || $tender->technicalbid_opening_date;

        if ($technicalBiddingOpened && !$tender->tender_opened_at) {
            // Technical review phase - load all biddings with relationships
            $tender->loadMissing([
                'biddings.biddingDocuments',
                'biddings.pricings.tenderitem',
                'biddings.pricings.bidding',
                'biddings.documentReview',
                'biddings.emdPayment'
            ]);
            $this->biddings = $tender->biddings;
        } else {
            // Financial phase or before technical opening - load accepted biddings with relationships
            // Include biddingDocuments for backward compatibility with old tenders
            $tender->loadMissing([
                'acceptedBiddings.biddingDocuments',
                'acceptedBiddings.pricings.tenderitem',
                'acceptedBiddings.pricings.bidding',
                'acceptedBiddings.documentReview',
                'acceptedBiddings.emdPayment'
            ]);
            $this->biddings = $tender->acceptedBiddings;
        }
    }

    public function download()
    {
        $data = $this->acceptedTenderBiddings;

        if (empty($data)) {
            $this->notify('No bidding data available for download.', 'error');
            return;
        }

        $filename = 'tender-financialbid-' . date('Y-m-d') . '-' . Str::random(6) . '.csv';

        return response()->streamDownload(function () use ($data, $filename) {
            $writer = SimpleExcelWriter::streamDownload($filename);
            $writer->addRows($data);
            $writer->close();
        }, $filename);

        // $headers = [
        //     'Cache-Control'       => 'must-revalidate, post-check=0, pre-check=0',
        //     'Content-type'        => 'text/csv',
        //     'Content-Disposition' => "attachment; filename=$filename",
        //     'Expires'             => '0',
        //     'Pragma'              => 'public',
        // ];

        // return response()->stream(function () use ($data) {
        //     $csvWriter = Writer::createFromFileObject(
        //         new SplFileObject('php://output', 'w+')
        //     );
        //     $csvWriter->insertOne(array_keys(collect($data)->first()));
        //     $csvWriter->insertAll($data);
        // }, 200, $headers);
    }

    public function getTenderProperty()
    {
        $tender = Tender::query()
            ->withExists(['biddingPrices', 'result', 'documentReviews'])
            ->findOrFail($this->tenderId);

        // Load appropriate relationships based on tender status
        // Check if technical bidding is opened (either field can indicate this)
        $technicalBiddingOpened = $tender->tender_document_opened_at || $tender->technicalbid_opening_date;

        if ($technicalBiddingOpened && !$tender->tender_opened_at) {
            // Technical review phase - load all biddings
            $tender->load([
                'biddings.pricings.tenderitem',
                'biddings.biddingDocuments',
                'biddings.documentReview',
                'biddings.emdPayment',
                'biddingCompanies',
                'tenderitems.biddingPrices.bidding',
                'result.bidding'
            ]);
        } else {
            // Financial phase or before technical opening - load accepted biddings
            // Include biddingDocuments for backward compatibility with old tenders
            $tender->load([
                'acceptedBiddings.pricings.tenderitem',
                'acceptedBiddings.biddingDocuments',
                'acceptedBiddings.documentReview',
                'acceptedBiddings.emdPayment',
                'biddingCompanies',
                'tenderitems.biddingPrices.bidding',
                'result.bidding'
            ]);
        }

        return $tender;
    }

    public function getBiddingsProperty()
    {
        // When technical document is opened, show all biddings for review
        // Otherwise, show only accepted biddings for financial evaluation
        // Check if technical bidding is opened (either field can indicate this)
        $technicalBiddingOpened = $this->tender->tender_document_opened_at || $this->tender->technicalbid_opening_date;

        if ($technicalBiddingOpened && !$this->tender->tender_opened_at) {
            return $this->tender->biddings;
        }

        return $this->tender->acceptedBiddings;
    }

    public function getAcceptedTenderBiddingsProperty()
    {
        return $this->tender->tenderitems->map(fn ($item) => [
            "item" => $item->item_type,
            "quantity" => $item->quantity,
            "unit" => $item->unit,
            "estimated_price" => $item->estimated_price,

            ...$item->biddingPrices->flatMap(function ($biddingPrice) {
                if ($biddingPrice->bidding) {
                    return [
                        $biddingPrice?->bidding?->company_name => "{$biddingPrice->bidding_price}" ?? 0
                    ];
                }
                return []; // Return empty array if no bidding
            })->all()
        ])->all();
    }

    public function isTenderOpeningDateToday()
    {
        return $this->tender->tender_opening_date && $this->tender->tender_opening_date <= now();
    }

    public function render()
    {
        $acceptedTenderBiddings = $this->acceptedTenderBiddings;

        return view('livewire.tenders.bidding', [
            'tender' => $this->tender,
            'biddingHeaders' => !empty($acceptedTenderBiddings) ? array_keys($acceptedTenderBiddings[0]) : []
        ]);
    }
}
