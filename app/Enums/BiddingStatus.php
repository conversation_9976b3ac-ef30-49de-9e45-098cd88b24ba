<?php

namespace App\Enums;

use App\Traits\EnumToArray;

enum BiddingStatus: string
{
    use EnumToArray;
    
    case PENDING = "pending";
    case APPROVED = "approved";
    case CANCELLED = "cancelled";

    public function color()
    {
        return match($this) {
            BiddingStatus::PENDING => 'warning',
            BiddingStatus::APPROVED => 'success',
            BiddingStatus::CANCELLED => 'danger',
            default => 'warning'
        };
    }
}
